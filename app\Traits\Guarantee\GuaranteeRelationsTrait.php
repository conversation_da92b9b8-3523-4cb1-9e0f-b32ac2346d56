<?php

namespace App\Traits\Guarantee;

use App\Models\Product\Product;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

/**
 * Guarantee Relations Trait
 *
 * This trait contains all relationship methods for the Guarantee model.
 * It helps to separate relationship logic from the core model functionality.
 *
 * @package App\Traits\Guarantee
 */
trait GuaranteeRelationsTrait
{
    /**
     * Get the products that this guarantee belongs to.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsToMany
     */
    public function products(): BelongsToMany
    {
        return $this->belongsToMany(Product::class);
    }
}
