<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Product\Product;
use App\Models\Product\ProductVariant;
use App\Models\Product\Guarantee;
use App\Models\Content\Article;
use App\Models\Content\Guide;

class DeleteClothProduct extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'product:delete-cloth';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Delete all related data for the cloth product (variants, guarantees, gallery, details, keywords, comments, questions, articles, guides, etc.) but keep the product itself';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting to delete cloth product and related data...');

        // Find the cloth product
        $product = Product::where('slug', 'cloth')->first();

        if (!$product) {
            $this->warn('No product with slug "cloth" found. Nothing to delete.');
            return 0;
        }

        $this->info("Found cloth product with ID: {$product->id}");

        // Delete variants and their attributes
        $variantsCount = ProductVariant::where('product_id', $product->id)->count();
        ProductVariant::where('product_id', $product->id)->delete();
        $this->info("Deleted {$variantsCount} product variants and their attributes.");

        // Delete guarantees
        $guaranteesCount = Guarantee::where('product_id', $product->id)->count();
        Guarantee::where('product_id', $product->id)->delete();
        $this->info("Deleted {$guaranteesCount} guarantees.");

        // Delete gallery images (they will be automatically deleted due to the morphMany relationship)
        $galleryCount = $product->gallery()->count();
        $product->gallery()->delete();
        $this->info("Deleted {$galleryCount} gallery images.");

        // Delete product details
        $detailsCount = $product->details()->count();
        $product->details()->delete();
        $this->info("Deleted {$detailsCount} product details.");

        // Delete keywords
        $keywordsCount = $product->keywords()->count();
        $product->keywords()->delete();
        $this->info("Deleted {$keywordsCount} keywords.");

        // Delete comments
        $commentsCount = $product->comments()->count();
        $product->comments()->delete();
        $this->info("Deleted {$commentsCount} comments.");

        // Delete questions and their answers
        $questionsCount = $product->questions()->count();
        foreach ($product->questions as $question) {
            $question->answers()->delete();
        }
        $product->questions()->delete();
        $this->info("Deleted {$questionsCount} questions and their answers.");

        // Delete articles
        $articlesCount = $product->articles()->count();
        $product->articles()->delete();
        $this->info("Deleted {$articlesCount} articles.");

        // Delete guides
        $guidesCount = $product->guides()->count();
        $product->guides()->delete();
        $this->info("Deleted {$guidesCount} guides.");

        // Detach categories
        $categoriesCount = $product->category()->count();
        $product->category()->detach();
        $this->info("Detached {$categoriesCount} categories.");

        // Detach delivery methods
        $deliveryMethodsCount = $product->deliveryMethods()->count();
        $product->deliveryMethods()->detach();
        $this->info("Detached {$deliveryMethodsCount} delivery methods.");

        // We're keeping the product itself, just deleting related data
        $this->info('Cloth product related data has been successfully deleted.');

        return 0;
    }
}
