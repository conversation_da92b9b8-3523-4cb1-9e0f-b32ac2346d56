<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Product\Product;
use App\Models\Product\Guarantee;
class GuaranteeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * Creates guarantees with Persian values for products
     */
    public function run()
    {

        try {

            $products = Product::all();

            if ($products->isEmpty()) {
                $this->command->info('No products found in the database. Skipping guarantee creation.');
                return;
            }
        } catch (\Exception $e) {
            $this->command->error('Error fetching products: ' . $e->getMessage());
            return;
        }

        // Persian guarantee company names
        $companyNames = [
            'گارانتی ایران',
            'پارس گارانتی',
            'گارانتی آسیا',
            'ضمانت پرداز',
            'تضمین کالا',
            'ایران سرویس',
            'خدمات پس از فروش سامان',
            'پشتیبان کالا',
            'ضمانت طلایی',
            'گارانتی پارسیان'
        ];

        // Guarantee durations in months (common in Iran)
        $guaranteeDurations = [12, 18, 24, 36];


        // Randomly decide which products get guarantees (approximately 60% chance)
        foreach ($products as $product) {
            // 60% chance to have a guarantee
            if (rand(1, 100) <= 60) {
                // If product gets a guarantee, create 1-3 guarantee options
                $guaranteeCount = rand(1, 3);

                for ($i = 0; $i < $guaranteeCount; $i++) {
                    $this->createGuarantee($product, $companyNames, $guaranteeDurations);
                }

                $this->command->info("گارانتی برای محصول {$product->title} ایجاد شد.");
            } else {
                $this->command->info("محصول {$product->title} بدون گارانتی است.");
            }
        }

        $this->command->info('گارانتی‌ها با موفقیت ایجاد شدند.');
    }

    /**
     * Create a guarantee with Persian values
     */
    private function createGuarantee($product, $companyNames, $guaranteeDurations)
    {
        try {

            // Generate a random price using the helper function
            $price = generateRandomFakeprice(5); // 5-digit price (e.g., 1,200,000 IRR)

            // Random guarantee duration
            $months = $guaranteeDurations[array_rand($guaranteeDurations)];

            // Random company name
            $companyName = $companyNames[array_rand($companyNames)];

            // Create the guarantee directly to avoid PDO issues
            $guarantee = new Guarantee();
            $guarantee->product_id = $product->id;
            $guarantee->price = $price;
            $guarantee->months = $months;
            $guarantee->company_name = $companyName;
            $guarantee->save();


            return true;
        } catch (\Exception $e) {
            $this->command->error('Error creating guarantee: ' . $e->getMessage());
            return false;
        }
    }
}
