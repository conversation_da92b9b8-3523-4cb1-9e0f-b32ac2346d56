<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

/**
 * Migration to move existing guarantee-product relationships to the pivot table
 */
return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // First, check if the product_id column still exists in guarantees table
        if (Schema::hasColumn('guarantees', 'product_id')) {
            // Move existing relationships to the pivot table
            DB::statement("
                INSERT INTO guarantee_product (guarantee_id, product_id, created_at, updated_at)
                SELECT id, product_id, created_at, updated_at
                FROM guarantees
                WHERE product_id IS NOT NULL
                ON CONFLICT DO NOTHING
            ");
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Clear the pivot table
        DB::table('guarantee_product')->truncate();
    }
};
