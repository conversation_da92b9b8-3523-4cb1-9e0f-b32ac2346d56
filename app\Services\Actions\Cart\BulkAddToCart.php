<?php

namespace App\Services\Actions\Cart;

use App\Models\Shopping\ShoppingCart;
use App\Models\Product\Product;
use App\Models\Product\ProductVariant;
use Illuminate\Support\Facades\DB;

/**
 * BulkAddToCart Action
 *
 * Handles adding multiple products to a user's shopping cart at once.
 * This action is responsible for:
 * - Creating or updating multiple cart items in a single transaction
 * - Handling authenticated user carts
 * - If a product already exists in the cart, it sets the quantity to the given value
 * - If a product doesn't exist in the cart, it creates a new cart item with the given quantity
 */
class BulkAddToCart
{
    /**
     * Process adding multiple items to the cart.
     *
     * @param array $data Input data containing:
     *                    - items: Array of items with:
     *                      - variant_id: (string) The ID of the product variant to add
     *                      - quantity: (int) The quantity to set, defaults to 1
     * @return \App\Models\Shopping\ShoppingCart The updated shopping cart with the new items added
     */
    public function handle(array $data)
    {
        // Get the authenticated user's ID directly
        $userId = auth()->id();
        $items = $data['items'] ?? [];

        // Use a database transaction to ensure all items are added atomically
        return DB::transaction(function () use ($userId, $items) {
            // Get or create a shopping cart for this user
            $cart = ShoppingCart::firstOrCreate(
                ['user_id' => $userId]
            );
            foreach ($items as $item) {
                $variantId = $item['variant_id'];
                $quantity = $item['quantity'] ?? 1;

                // Find the product variant
                $variant = ProductVariant::find($variantId);


                // Find the parent product
                $product = Product::find($variant->product_id);


                // Check if this product variant is already in the cart
                $existing = $cart->items()
                    ->where('product_id', $product->id)
                    ->where('product_variant_id', $variant->id)
                    ->first();

                if ($existing) {
                    // If item already exists in cart, set the quantity to the given value
                    // instead of adding to it
                    $existing->quantity = $quantity;
                    $existing->save();
                } else {
                    // If item doesn't exist in cart, create a new cart item
                    // We store a snapshot of product data to preserve pricing and details
                    $cart->items()->create([
                        'product_id' => $product->id,
                        'product_variant_id' => $variant->id,
                        'name' => $product->title,
                        'price' => $variant->price,
                        'sale_price' => $variant->sale_price,
                        'quantity' => $quantity,
                        'image' => $variant->image ?? $product->main_image ?? null,
                    ]);

                }
            }

            
            return $cart->load('items.productVariant.product', 'items.productVariant.attributes');
        });
    }
}
