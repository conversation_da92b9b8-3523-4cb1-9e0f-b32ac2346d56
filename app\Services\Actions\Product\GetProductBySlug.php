<?php

namespace App\Services\Actions\Product;

use App\Models\Product\Product;

/**
 * Action class to retrieve a product by its slug.
 */
class GetProductBySlug
{
    /**
     * Retrieve a product by its slug with all related data.
     *
     * @param array $data An array containing the slug key
     * @return Product|null The product if found, null otherwise
     */
    public function handle(array $data): ?Product
    {

        $product = Product::with([
            'gallery',
            'details',
            'keywords',
            'variants.attributes',
            'variants.purchases',
            'guarantees',
            'deliveryMethods',
            'shop',
            'category',
        ])
            ->where('slug', $data['slug'])
            ->first();
        dd($product->guarantees);

        return $product;
    }
}
