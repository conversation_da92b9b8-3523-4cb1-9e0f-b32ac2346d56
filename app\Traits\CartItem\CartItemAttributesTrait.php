<?php

namespace App\Traits\CartItem;

/**
 * CartItem Attributes Trait
 *
 * This trait contains all attribute methods for the CartItem model.
 * It helps to separate attribute logic from the core model functionality.
 *
 * @package App\Traits\CartItem
 */
trait CartItemAttributesTrait
{
    /**
     * Get the effective price for this cart item (sale_price if available, otherwise regular price).
     *
     * @return float
     */
    public function getEffectivePriceAttribute()
    {
        return isset($this->sale_price) && $this->sale_price != null ? $this->sale_price : $this->price;
    }


    /**
     * Calculate the discount amount for this cart item.
     * If there's no sale price or it's equal to the regular price, discount is 0.
     *
     * @return float
     */
    public function getDiscountAttribute()
    {
        if (!isset($this->sale_price) || $this->sale_price == null || $this->sale_price >= $this->price) {
            return 0;
        }

        return ($this->price - $this->sale_price) * $this->quantity;
    }

    /**
     * Calculate the total price for this cart item (effective price * quantity + guarantee price * quantity).
     *
     * @return float
     */
    public function getTotalAttribute()
    {
        $productTotal = $this->effective_price * $this->quantity;
        $guaranteeTotal = ($this->guarantee_price ?? 0) * $this->quantity;

        return $productTotal + $guaranteeTotal;
    }

    /**
     * Calculate the subtotal price for this cart item (effective price * quantity).
     *
     * @return float
     */
    public function getSubtotalAttribute()
    {
        return $this->price * $this->quantity;
    }
}
