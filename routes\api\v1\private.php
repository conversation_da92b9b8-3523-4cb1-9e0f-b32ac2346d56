<?php
/**
 * Private API Routes
 *
 * This file contains all private API routes that require authentication.
 * These endpoints are only accessible to authenticated users.
 *
 * Base URI: /api/v1
 */

use App\Http\Controllers\Api\V1\Private\Product\Favorites\ProductFavoritesController;
use App\Http\Controllers\Api\V1\Private\Product\ProductAttributeController;
use App\Http\Controllers\Api\V1\Private\Product\ProductCategoryController;
use App\Http\Controllers\Api\V1\Private\Comment\CommentStatusController;
use App\Http\Controllers\Api\V1\Private\Discount\DiscountController;
use App\Http\Controllers\Api\V1\Private\Discount\DiscountStatusController;
use App\Http\Controllers\Api\V1\Private\Product\ProductController;
use App\Http\Controllers\Api\V1\Private\Product\ProductQuestionController;
use App\Http\Controllers\Api\V1\Private\Product\ProductVariantController;
use App\Http\Controllers\Api\V1\Private\User\AddressController;
use App\Http\Controllers\Api\V1\Private\Shopping\CartController;
use App\Http\Controllers\Api\V1\Private\Shopping\BulkCartController;
use App\Http\Controllers\Api\V1\Private\Shopping\InvoiceController;
use App\Http\Controllers\Api\V1\Private\Shopping\InvoicePaymentController;
use App\Http\Controllers\Api\V1\Private\User\UserNotificationController;
use App\Http\Controllers\Api\V1\Private\User\UserProfileController;
use App\Http\Controllers\Api\V1\Private\User\WalletTopUpController;
use App\Http\Controllers\Api\V1\Private\Product\ProductImageController;
use App\Http\Controllers\Api\V1\Private\SellerAccounting\SellerAccountsController;
use App\Http\Controllers\Ticket\TicketController;
use App\Http\Controllers\Ticket\TicketMessageController;
use Illuminate\Support\Facades\Route;

/**
 * All routes in this file require authentication
 *
 * @authenticated
 */
Route::middleware('jwt')->group(function () {
    /**
     * User profile routes
     *
     * @authenticated
     */
    Route::get('/profile', [UserProfileController::class, 'index'])->name('profile.index');
    Route::post('/profile', [UserProfileController::class, 'update'])->name('profile.update');
    /**
     * Address routes
     *
     * @authenticated
     */
    Route::resource('addresses', AddressController::class)->only([
        'index',
        'store',
        'update',
        'destroy'
    ])->parameters(['addresses' => "address_id"]);

    /**
     * Cart routes
     *
     * @authenticated
     */
    Route::resource('cart', CartController::class)->only([
        'index',
        'store'
    ]);
    Route::delete('cart/{variant_id}', [CartController::class, 'destroy']);

    /**
     * Bulk cart operations
     *
     * @authenticated
     */
    Route::resource('bulk-cart', BulkCartController::class)->only([
        'store'
    ]);

    /**
     * Invoice routes
     *
     * @authenticated
     */
    Route::resource('invoices', InvoiceController::class)->only([
        'index',
        'store',
        'show'
    ])->parameters(['invoices' => 'invoice_id']);

    /**
     * Invoice Payment routes
     *
     * @authenticated
     */
    Route::resource('invoices/pay', InvoicePaymentController::class)->only([
        'store'
    ]);

    /**
     * Wallet Top-up routes
     *
     * @authenticated
     */
    Route::resource('wallet/topup', WalletTopUpController::class)->only([
        'store'
    ]);


    /**
     * Product Images routes
     *
     * @authenticated
     */
    Route::resource('products.images', ProductImageController::class)->only(['store', 'destroy']);



    /**
     * Product store route
     *
     * @authenticated
     */
    Route::resource('products', ProductController::class)->only(['store', 'update']);

    /**
     *  products Category  routes
     *
     * @authenticated
     */
    Route::post('products/{product}/categories/{category}', [ProductCategoryController::class, 'store']);
    Route::delete('products/{product}/categories/{category}', [ProductCategoryController::class, 'destroy']);

    /**
     *  products attributes  routes
     *
     * @authenticated
     */
    Route::resource("products/attributes", ProductAttributeController::class)->only(['store']);

    /**
     *  products variants  routes
     *
     * @authenticated
     */
    Route::resource("products/variants", ProductVariantController::class)->only(['store']);

    /**
     *  comments status  routes
     *
     * @authenticated
     */
    Route::resource("comments/status", controller: CommentStatusController::class)->only(['store']);

    /**
     *  products questions  routes
     *
     * @authenticated
     */
    Route::resource("products/questions/", ProductQuestionController::class)->only(['store']);

    /**
     *  products favorite  routes
     *
     * @authenticated
     */
    Route::resource("products/favorite/", ProductFavoritesController::class)->only(['store', 'index']);

    /**
     *  user notifications  routes
     *
     * @authenticated
     */
    Route::resource("user/notifications/", UserNotificationController::class)->only(['index']);

    /**
     *  user tickets routes
     *
     * @authenticated
     */
    Route::resource("user/ticket/", TicketController::class)->only(['index', 'store']);

    /**
     *  user ticket messages  routes
     *
     * @authenticated
     */
    Route::resource("user/ticket/messages", TicketMessageController::class)->only(['store']);
    Route::get("user/ticket/{ticket}/messages", [TicketMessageController::class, 'index']);

    /**
     *  seller accounts routes
     *
     * @authenticated
     */
    Route::resource("seller/accounts", SellerAccountsController::class)->only(['index', 'store']);

    /**
     *  discount accounts routes
     *
     * @authenticated
     */
    Route::resource("discount-codes", DiscountController::class)->only(['index', 'store']);
    Route::resource("discount-codes/status", DiscountStatusController::class)->only(['store']);

});
