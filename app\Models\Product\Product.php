<?php

namespace App\Models\Product;

use App\Enums\Product\StatusEnum;
use App\Traits\Product\ProductRelationsTrait;
use App\Traits\Product\ProductAttributesTrait;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;

class Product extends Model
{
    use ProductRelationsTrait, ProductAttributesTrait;



    protected $fillable = [
        'title',
        'description',
        'slug',
        'meta_title',
        'meta_description',
        'delivery_method_ids',
        'shop_id',
        'rate',
        'seller_status',
        'product_unit_id',
        'admin_status',
        'category_id'
    ];

    protected $casts = [
        'keyword_id' => 'string',
        'guarantees' => 'array',

        'keywordable_id' => 'string',
        'shop_id' => 'string',
        'guarantee_ids' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'status' => StatusEnum::class,
    ];
    /**
     * Search products using PostgreSQL full-text search with multi-language support
     */
    public function scopeSearch($query, $searchTerm)
    {
        if (empty($searchTerm)) {
            return $query;
        }

        $searchTerm = $this->formatSearchTerm($searchTerm);

        // Use both full-text search and ILIKE for better multi-language support
        return $query->where(function ($q) use ($searchTerm) {
            // Full-text search using 'simple' configuration (works better with non-English)
            $q->whereRaw(
                "to_tsvector('simple', coalesce(title, '') || ' ' || coalesce(description, '')) @@ plainto_tsquery('simple', ?)",
                [$searchTerm]
            )
                // Fallback to trigram similarity search
                ->orWhere('title', 'ILIKE', "%{$searchTerm}%")
                ->orWhere('description', 'ILIKE', "%{$searchTerm}%");
        })
            ->orderByRaw(
                "GREATEST(
                similarity(title, ?),
                similarity(description, ?),
                ts_rank(to_tsvector('simple', coalesce(title, '') || ' ' || coalesce(description, '')), plainto_tsquery('simple', ?))
            ) DESC",
                [$searchTerm, $searchTerm, $searchTerm]
            );
    }

    /**
     * Search only in titles
     */
    public function scopeSearchTitle($query, $searchTerm)
    {
        if (empty($searchTerm)) {
            return $query;
        }

        $searchTerm = $this->formatSearchTerm($searchTerm);

        return $query->where(function ($q) use ($searchTerm) {
            $q->whereRaw(
                "to_tsvector('simple', title) @@ plainto_tsquery('simple', ?)",
                [$searchTerm]
            )
                ->orWhere('title', 'ILIKE', "%{$searchTerm}%");
        })
            ->orderByRaw(
                "GREATEST(
                similarity(title, ?),
                ts_rank(to_tsvector('simple', title), plainto_tsquery('simple', ?))
            ) DESC",
                [$searchTerm, $searchTerm]
            );
    }

    /**
     * Search only in descriptions
     */
    public function scopeSearchDescription($query, $searchTerm)
    {
        if (empty($searchTerm)) {
            return $query;
        }

        $searchTerm = $this->formatSearchTerm($searchTerm);

        return $query->where(function ($q) use ($searchTerm) {
            $q->whereRaw(
                "to_tsvector('simple', description) @@ plainto_tsquery('simple', ?)",
                [$searchTerm]
            )
                ->orWhere('description', 'ILIKE', "%{$searchTerm}%");
        })
            ->orderByRaw(
                "GREATEST(
                similarity(description, ?),
                ts_rank(to_tsvector('simple', description), plainto_tsquery('simple', ?))
            ) DESC",
                [$searchTerm, $searchTerm]
            );
    }

    /**
     * Advanced search with ranking and highlighting
     */
    public function scopeAdvancedSearch($query, $searchTerm, $options = [])
    {
        if (empty($searchTerm)) {
            return $query;
        }

        $searchTerm = $this->formatSearchTerm($searchTerm);
        $minRank = $options['min_rank'] ?? 0;

        $query = $query->selectRaw('
            products.*,
            GREATEST(
                similarity(title, ?),
                similarity(description, ?),
                ts_rank(to_tsvector(\'simple\', coalesce(title, \'\') || \' \' || coalesce(description, \'\')), plainto_tsquery(\'simple\', ?))
            ) as search_rank,
            ts_headline(\'simple\', title, plainto_tsquery(\'simple\', ?), \'MaxWords=20\') as highlighted_title,
            ts_headline(\'simple\', description, plainto_tsquery(\'simple\', ?), \'MaxWords=50\') as highlighted_description
        ', [$searchTerm, $searchTerm, $searchTerm, $searchTerm, $searchTerm])
            ->where(function ($q) use ($searchTerm) {
                $q->whereRaw(
                    "to_tsvector('simple', coalesce(title, '') || ' ' || coalesce(description, '')) @@ plainto_tsquery('simple', ?)",
                    [$searchTerm]
                )
                    ->orWhere('title', 'ILIKE', "%{$searchTerm}%")
                    ->orWhere('description', 'ILIKE', "%{$searchTerm}%");
            });

        if ($minRank > 0) {
            $query->havingRaw('search_rank > ?', [$minRank]);
        }

        return $query->orderBy('search_rank', 'desc');
    }

    public function scopeWithSellerResourceDefaults(){
        $this->load('gallery');
    }
    /**
     * Format search term for PostgreSQL full-text search
     */
    private function formatSearchTerm($term)
    {
        // Remove special characters and normalize
        $term = preg_replace('/[^\w\s]/', ' ', $term);
        $term = preg_replace('/\s+/', ' ', trim($term));

        return $term;
    }

    /**
     * Get search suggestions based on existing titles
     */
    public static function getSearchSuggestions($searchTerm, $limit = 5)
    {
        if (empty($searchTerm)) {
            return collect();
        }

        return self::select('title')
            ->where('title', 'ILIKE', "%{$searchTerm}%")
            ->distinct()
            ->limit($limit)
            ->pluck('title');
    }
}
