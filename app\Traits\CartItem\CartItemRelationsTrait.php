<?php

namespace App\Traits\CartItem;

use App\Models\Product\ProductVariant;
use App\Models\Shopping\ShoppingCart;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * CartItem Relations Trait
 *
 * This trait contains all relationship methods for the CartItem model.
 * It helps to separate relationship logic from the core model functionality.
 *
 * @package App\Traits\CartItem
 */
trait CartItemRelationsTrait
{
    /**
     * Get the shopping cart that this item belongs to.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function cart()
    {
        return $this->belongsTo(ShoppingCart::class);
    }

    /**
     * Get the product variation that this item belongs to.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */

    public function productVariant(): BelongsTo
    {
        return $this->belongsTo(ProductVariant::class);
    }
}
