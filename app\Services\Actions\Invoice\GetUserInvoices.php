<?php

namespace App\Services\Actions\Invoice;

use App\Enums\InvoiceDeliveryStatusEnum;
use App\Models\Shopping\Invoice;
use App\Traits\Pagination;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;

/**
 * Action class for retrieving invoices for a user.
 */
class GetUserInvoices
{
    use Pagination;
    /**
     * Retrieve all invoices for a user.
     *
     * @param array $data Empty array as we don't need any data from the request
     * @return LengthAwarePaginator Collection of invoices
     */
    public function handle(array $data): LengthAwarePaginator
    {

        // Get the authenticated user's ID directly
        $userId = auth()->id();
        $deliveryStatus = isset($data['delivery_status']) ? InvoiceDeliveryStatusEnum::fromString($data['delivery_status']) : null;
        // Retrieve all invoices for the user with their products and transactions, ordered by creation date (newest first)
        $builder = Invoice::where('user_id', $userId)
            ->with([
                'products.details',
                'products.productVariant.product.gallery',
                'products.productVariant.attributes',
                'transactions'
            ])
            ->when($deliveryStatus, function ($query) use ($deliveryStatus) {
                return $query->where('delivery_status', $deliveryStatus);
            })
            ->latest();

        return $this->applyPagination($builder, $data);

    }
}
