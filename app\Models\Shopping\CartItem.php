<?php

namespace App\Models\Shopping;

use App\Traits\CartItem\CartItemRelationsTrait;
use App\Traits\CartItem\CartItemAttributesTrait;
use Illuminate\Database\Eloquent\Model;

/**
 * CartItem Model
 *
 * Represents an item in a shopping cart with product details and quantity.
 * Each cart item belongs to a shopping cart and contains a snapshot of product information.
 *
 * @property string $id The unique identifier for the cart item
 * @property string $cart_id The ID of the shopping cart this item belongs to
 * @property string $product_id The ID of the product
 * @property string $variant_id The ID of the product variation
 * @property string $name The name/title of the product (snapshot)
 * @property float $price The regular price of the product variation (snapshot)
 * @property float|null $sale_price The sale price of the product variation (snapshot)
 * @property int $quantity The quantity of this item in the cart
 * @property string|null $image The image URL for this item (snapshot)
 * @property-read float $total The calculated total price (considering sale price if available)
 * @property-read float $discount The calculated discount amount for this item
 * @property-read ShoppingCart $cart The shopping cart this item belongs to
 */
class CartItem extends Model
{
    use CartItemRelationsTrait, CartItemAttributesTrait;
    protected $fillable = [
        'product_id',
        'product_variant_id',
        'shopping_cart_id',
        'name',
        'price',
        'sale_price',
        'quantity',
        'image',
    ];


    public $timestamps = false;


}
