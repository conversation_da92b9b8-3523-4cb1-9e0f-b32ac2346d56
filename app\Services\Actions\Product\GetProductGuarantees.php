<?php

namespace App\Services\Actions\Product;

use App\Models\Product\Guarantee;
use Illuminate\Database\Eloquent\Collection;

/**
 * Get Product Guarantees Action
 *
 * Retrieves all guarantees available for a specific product.
 */
class GetProductGuarantees
{
    /**
     * Handle retrieving guarantees for a product.
     *
     * @param array $data [
     *   'product' => Product - The product model instance
     * ]
     * @return Collection
     */
    public function handle(array $data): Collection
    {
        $product = $data['product'];

        // Get all active guarantees for this product
        $guarantees = Guarantee::where('product_id', $product->id)
            ->orderBy('price', 'asc') // Order by price ascending
            ->get();

        return $guarantees;
    }
}
