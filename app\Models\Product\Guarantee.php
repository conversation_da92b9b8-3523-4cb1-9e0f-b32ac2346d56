<?php

namespace App\Models\Product;

use App\Traits\Guarantee\GuaranteeRelationsTrait;
use Illuminate\Database\Eloquent\Model;
use MongoDB\Laravel\Eloquent\SoftDeletes;

class Guarantee extends Model
{
    use SoftDeletes, GuaranteeRelationsTrait;

    
    

    protected $fillable = [
        'product_id',
        'price',
        'months',
        'company_name',
    ];

    protected $casts = [
        'price' => 'float',
        'months' => 'integer',
        'product_id' => 'string',
    ];

}
