<?php

namespace App\Models\Product;

use App\Traits\Guarantee\GuaranteeRelationsTrait;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Guarantee extends Model
{
    use SoftDeletes, GuaranteeRelationsTrait;

    protected $fillable = [
        'price',
        'months',
        'company_name',
    ];

    protected $casts = [
        'price' => 'float',
        'months' => 'integer',
    ];

}
