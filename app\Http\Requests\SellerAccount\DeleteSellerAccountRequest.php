<?php

namespace App\Http\Requests\SellerAccount;

use App\Models\SellerAccounting\SellerAccount;
use Illuminate\Foundation\Http\FormRequest;

/**
 * Delete Seller Account Request
 *
 * Validates the request data for deleting a seller account.
 */
class DeleteSellerAccountRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        // Only authenticated sellers can delete their accounts
        return auth()->check() && auth()->user()->isSeller();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'account_id' => [
                'required',
                'string',
                function ($attribute, $value, $fail) {
                    // Find the seller account
                    $account = SellerAccount::find($value);

                    // Check if account exists
                    if (!$account) {
                        $fail(__('messages.seller_accounts.not_found'));
                        return;
                    }

                    // Check if the account belongs to the authenticated user
                    if ($account->user_id != auth()->id()) {
                        $fail(__('messages.seller_accounts.unauthorized'));
                        return;
                    }

                    // Check if account is verified (prevent deletion of verified accounts)
                    if ($account->verified) {
                        $fail(__('messages.seller_accounts.cannot_delete_verified'));
                        return;
                    }
                },
            ],
        ];
    }

    /**
     * Prepare the data for validation.
     *
     * @return void
     */
    protected function prepareForValidation(): void
    {
        // Add the account ID from the route parameter
        $this->merge([
            'account_id' => $this->route('account'),
        ]);
    }
}
