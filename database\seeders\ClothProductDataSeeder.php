<?php

namespace Database\Seeders;

use App\Enums\Product\StatusEnum;
use Illuminate\Database\Seeder;
use App\Models\Product\Product;
use App\Models\Product\ProductVariant;
use App\Models\Product\Category;
use App\Models\Shopping\DeliveryMethod;
use App\Models\Product\Guarantee;
use App\Models\Product\ProductDetail;
use App\Models\User\User;
use App\Models\User\Shop;
use Str;
use <PERSON><PERSON><PERSON><PERSON>\Faker\Facades\Faker;
use Illuminate\Support\Facades\Hash;

class ClothProductDataSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * This seeder:
     * 1. Creates or finds the cloth product
     * 2. Creates variants with attributes
     * 3. Attaches categories
     * 4. Attaches delivery methods
     * 5. <PERSON>reates guarantees
     * 6. Creates gallery images
     * 7. Creates product details
     * 8. Creates keywords for the product
     * 9. Creates comments for the product
     * 10. Creates questions and answers for the product
     * 11. Creates articles for the product
     * 12. Creates guides for the product
     */
    public function run(): void
    {
        $this->command->info('Starting cloth product seeder...');

        // Step 1: Create or find the cloth product
        $product = $this->createOrFindClothProduct();

        if (!$product) {
            $this->command->error('Failed to create or find cloth product. Exiting.');
            return;
        }

        // Step 2: Create variants with attributes
        $this->createvariants($product);

        $this->command->info("Product found: {$product->title} (ID: {$product->id})");

        // Attach delivery methods
        $this->attachDeliveryMethods($product);

        // Create guarantees
        $this->createGuarantees($product);

        // Create gallery images
        $this->createGalleryImages($product);

        // Create product details
        $this->createProductDetails($product);

        // Create keywords
        $this->createKeywords($product);

        // Create comments
        $this->createComments($product);

        // Create questions and answers
        $this->createQuestions($product);

        // Create purchase entries for variants
        $this->createPurchaseEntries($product);

        // Create guides for the product
        $this->createGuides($product);

        // Create articles for the product
        $this->createArticles($product);

        $this->command->info('All data successfully attached to the cloth product!');
    }

    /**
     * Create purchase entries for the product variants
     */
    private function createPurchaseEntries(Product $product): void
    {
        // Get all variants for this product
        $variants = $product->variants;

        if ($variants->isEmpty()) {
            $this->command->warn('No variants found. Skipping purchase entry creation.');
            return;
        }

        // Create purchase entries for each variation
        foreach ($variants as $variation) {
            // Create 1-3 purchase entries for each variation
            $numEntries = rand(1, 3);

            for ($i = 0; $i < $numEntries; $i++) {
                $variation->purchases()->create([
                    'quantity' => rand(5, 20), // Positive quantity for purchases
                    'price' => rand(100, 2000) / 100,
                    'purchased_at' => now()->subDays(rand(0, 30)),
                    'invoice_id' => null, // No invoice for purchases
                ]);
            }
        }

        $this->command->info("Purchase entries created for {$variants->count()} variants.");
    }

    /**
     * Create or find the cloth product
     */
    private function createOrFindClothProduct(): ?Product
    {
        try {
            // Get categories
            $category = Category::where('slug', 'summer-collection')->first();

            if (!$category) {
                $this->command->warn('No categories found. Skipping category attachment.');
                return null;
            }

            // Attach categories to product

            // Get all shops and pick a random one
            // Note: MongoDB doesn't support inRandomOrder(), so we need to get all shops and shuffle them
            $shops = Shop::all();

            if ($shops->isEmpty()) {
                $this->command->warn('No shops found. Make sure to run ShopSeeder first.');
                return null;
            }

            // Get a random shop
            $shop = $shops->shuffle()->first();

            $product = Product::firstOrCreate([
                'slug' => 'cloth',
            ], [
                'title' => 'پارچه',
                'description' => 'محصول پارچه با کیفیت عالی',
                'meta_title' => 'پارچه با کیفیت',
                'meta_description' => 'پارچه با کیفیت عالی برای انواع لباس',
                'shop_id' => $shop->id,
                'category_id' => $category->id,
            ]);

            $this->command->info($product->wasRecentlyCreated ? 'Cloth product created.' : 'Cloth product found.');

            return $product;
        } catch (\Exception $e) {
            $this->command->error('Error creating/finding product: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Create variants with attributes for the cloth product
     */
    private function createvariants(Product $product): void
    {
        try {
            // Delete existing variants to avoid duplicates
            ProductVariant::where('product_id', $product->id)->delete();

            $sizes = ['X', 'XL'];
            $colors = [
                ['title' => 'قرمز', 'extra_data' => ['hex' => '#ff0000']],
                ['title' => 'آبی', 'extra_data' => ['hex' => '#0000ff']],
            ];

            foreach ($sizes as $size) {
                foreach ($colors as $color) {
                    $variation = ProductVariant::create([
                        'product_id' => (string) $product->id,
                        'sku' => strtoupper("CLOTH-{$size}-{$color['title']}"),
                        'price' => generateRandomFakeprice()
                    ]);

                    $variation->attributes()->createMany([
                        [
                            'attribute_title' => 'سایز',
                            'attribute_value' => $size,
                            'attribute_type' => 'size',
                        ],
                        [
                            'attribute_title' => 'رنگ',
                            'attribute_value' => $color['title'],
                            'attribute_type' => 'color',
                            'extra_data' => $color['extra_data'],
                        ]
                    ]);
                }
            }

            $this->command->info(count($sizes) * count($colors) . ' variants created for the cloth product.');
        } catch (\Exception $e) {
            $this->command->error('Error creating variants: ' . $e->getMessage());
        }
    }

    /**
     * Attach delivery methods to the product
     */
    private function attachDeliveryMethods(Product $product): void
    {
        // Get all delivery methods
        $deliveryMethods = DeliveryMethod::all();

        if ($deliveryMethods->count() < 2) {
            $this->command->warn('Not enough delivery methods available. Skipping delivery method attachment.');
            return;
        }

        // Shuffle and take 2 random delivery methods
        $randomDeliveryMethods = $deliveryMethods->shuffle()->take(2);


        $product->deliveryMethods()->sync($randomDeliveryMethods);

    }

    /**
     * Add gallery images to the product
     */
    private function addGalleryImages(Product $product): void
    {
        try {
            // Delete existing gallery images to avoid duplicates
            $product->gallery()->delete();

            // High-quality fabric/cloth images with Persian captions
            $images = [
                [
                    'url' => 'https://picsum.photos/800/400',
                    'caption' => 'پارچه نخی با کیفیت عالی'
                ],
                [
                    'url' => 'https://picsum.photos/800/400',
                    'caption' => 'پارچه در رنگ‌های متنوع'
                ],
                [
                    'url' => 'https://picsum.photos/800/400',
                    'caption' => 'بافت نزدیک پارچه'
                ],
                [
                    'url' => 'https://picsum.photos/800/400',
                    'caption' => 'نمونه استفاده از پارچه'
                ],
                [
                    'url' => 'https://picsum.photos/800/400',
                    'caption' => 'پارچه طرح‌دار'
                ],
            ];

            // Add all images to the gallery
            foreach ($images as $image) {
                $product->gallery()->create([
                    'image_url' => $image['url'],
                    'caption' => $image['caption'],
                ]);
            }

            $this->command->info(count($images) . ' تصویر به گالری محصول پارچه اضافه شد.');
        } catch (\Exception $e) {
            $this->command->error('خطا در اضافه کردن تصاویر گالری: ' . $e->getMessage());
        }
    }

    /**
     * Create guarantees for the product
     */
    private function createGuarantees(Product $product): void
    {
        // Delete existing guarantees for this product
        Guarantee::where('product_id', $product->id)->delete();

        // Persian guarantee company names
        $companyNames = [
            'گارانتی ایران',
            'پارس گارانتی',
            'گارانتی آسیا',
        ];

        // Guarantee durations in months
        $guaranteeDurations = [12, 24, 36];

        // Create 2 guarantees
        for ($i = 0; $i < 2; $i++) {
            // Generate a random price
            $price = generateRandomFakeprice(5);

            // Random guarantee duration
            $months = $guaranteeDurations[array_rand($guaranteeDurations)];

            // Random company name
            $companyName = $companyNames[array_rand($companyNames)];

            // Create the guarantee
            $guarantee = new Guarantee();
            $guarantee->price = $price;
            $guarantee->months = $months;
            $guarantee->company_name = $companyName;
            $guarantee->save();

            // Attach the guarantee to the product using many-to-many relationship
            $product->guarantees()->attach($guarantee->id);
        }

        $this->command->info("Two guarantees created for the product");
    }

    /**
     * Create gallery images for the product
     */
    private function createGalleryImages(Product $product): void
    {
        // Delete existing gallery images for this product
        $product->gallery()->delete();

        // Sample image URLs
        $imageUrls = [
            'https://picsum.photos/800/400',
            'https://picsum.photos/800/400',
            'https://picsum.photos/800/400',
            'https://picsum.photos/800/400',
            'https://picsum.photos/800/400',
        ];

        // Create 3 random gallery images
        foreach (collect($imageUrls)->shuffle()->take(3) as $url) {
            $product->gallery()->create([
                'image_url' => $url,
                'caption' => "تصویر {$product->title}",
            ]);
        }

        $this->command->info("Three gallery images created for the product");
    }

    /**
     * Create product details for the product
     */
    private function createProductDetails(Product $product): void
    {
        // Delete existing product details for this product
        ProductDetail::where('product_id', $product->id)->delete();

        // Sample product details
        $details = [
            'جنس' => 'نخی',
            'کشور سازنده' => 'ایران',
            'قابل شستشو' => 'بله',
            'ضد آب' => 'خیر',
            'وزن' => '200 گرم',
        ];

        // Create product details
        foreach ($details as $key => $value) {
            $product->details()->create([
                'key' => $key,
                'value' => $value,
            ]);
        }

        $this->command->info("Product details successfully created");
    }

    /**
     * Create keywords for the product
     */
    private function createKeywords(Product $product): void
    {
        // Count existing keywords before deletion
        $keywordCount = $product->keywords()->count();

        // Delete existing keywords for this product
        $product->keywords()->delete();

        // Sample keywords
        $keywords = ['eco', 'premium', 'handmade', 'organic', 'local'];

        // Create 3 random keywords
        foreach (collect($keywords)->shuffle()->take(3) as $keyword) {
            $product->keywords()->create([
                'title' => $keyword,
            ]);
        }

        $this->command->info("Deleted {$keywordCount} keywords.");
        $this->command->info("Three keywords created for the product");
    }

    /**
     * Create comments for the product
     */
    private function createComments(Product $product): void
    {
        // Count existing comments before deletion
        $commentCount = $product->comments()->count();

        // Delete existing comments for this product
        $product->comments()->delete();

        // Get some users (or create a dummy user if none exist)
        $users = User::all();

        if ($users->isEmpty()) {
            $this->command->warn('No users found. Creating comments with null user_id.');
        }

        // Ensure users have full names
        $users = $this->ensureUsersHaveFullNames($users);

        // Create 5 comments, some with replies
        for ($i = 0; $i < 5; $i++) {
            // Generate random comment body
            $body = Faker::sentence();

            // Generate random rating between 1 and 5
            $rate = rand(1, 5);

            // Randomly decide if the user has bought the product
            $hasBought = (bool) rand(0, 1);

            // Randomly select a user for the comment (if available)
            $userId = $users->isNotEmpty() ? $users->random()->id : null;

            // Create the parent comment
            $comment = $product->comments()->create([
                'body' => $body,
                'rate' => $rate,
                'has_bought' => $hasBought,
                'user_id' => $userId,
                'status' => StatusEnum::CONFIRMED,
                'ip_address' => '127.0.0.1',
                'user_agent' => 'ClothProductSeeder/1.0',
            ]);

            // For the first 3 comments, add 1-3 replies
            if ($i < 3) {
                // Randomly decide how many replies to add (1-3)
                $replyCount = rand(1, 3);

                $commentNumber = $i + 1;
                $this->command->info("Adding {$replyCount} replies to comment {$commentNumber}");

                for ($j = 0; $j < $replyCount; $j++) {
                    // Generate random reply body
                    $replyBody = substr(Faker::sentence(), 0, 250);
                    // Randomly select a user for the reply (if available)
                    $replyUserId = $users->isNotEmpty() ? $users->random()->id : null;
                    // Create the reply comment with parent_id set to the parent comment's ID
                    $product->comments()->create([
                        'body' => $replyBody,
                        'parent_id' => $comment->id, // Set parent_id to create a reply
                        'user_id' => $replyUserId,
                        'ip_address' => '127.0.0.1',
                        'user_agent' => 'ClothProductSeeder/1.0',
                    ]);
                }
            }
        }

        $this->command->info("Deleted {$commentCount} comments.");
        $this->command->info("Five comments created for the product with replies");
    }

    /**
     * Create questions and answers for the product
     */
    private function createQuestions(Product $product): void
    {
        // Count existing questions before deletion
        $questionCount = $product->questions()->count();

        // Delete existing questions for this product
        $product->questions()->delete();

        // Get some users (or create a dummy user if none exist)
        $users = User::all();

        if ($users->isEmpty()) {
            $this->command->warn('No users found. Creating questions with null user_id.');
        }

        // Ensure users have full names
        $users = $this->ensureUsersHaveFullNames($users);

        // Predefined questions and answers for cloth product
        $questionsAndAnswers = [
            [
                'question' => 'آیا این پارچه قابل شستشو در ماشین لباسشویی است؟',
                'answer' => 'بله، این پارچه قابل شستشو در ماشین لباسشویی است. توصیه می‌شود از آب سرد و دور ملایم استفاده کنید و از مواد سفیدکننده خودداری نمایید تا رنگ و کیفیت پارچه حفظ شود.'
            ],
            [
                'question' => 'آیا این پارچه برای دوخت لباس کودک مناسب است؟',
                'answer' => 'بله، این پارچه به دلیل نرمی و لطافت برای پوست حساس کودکان مناسب است. همچنین به دلیل مقاومت بالا در برابر شستشوهای مکرر، گزینه خوبی برای لباس کودکان محسوب می‌شود.'
            ],
            [
                'question' => 'آیا امکان خرید متراژ کمتر از یک متر وجود دارد؟',
                'answer' => 'متأسفانه در حال حاضر امکان فروش کمتر از یک متر وجود ندارد. حداقل سفارش برای این پارچه یک متر است. البته برای سفارشات عمده و تجاری می‌توانید با پشتیبانی تماس بگیرید.'
            ],
            [
                'question' => 'آیا این پارچه ضد آب است؟',
                'answer' => 'خیر، این پارچه به صورت طبیعی ضد آب نیست. اما می‌توانید با استفاده از اسپری‌های مخصوص ضد آب کردن پارچه که در بازار موجود است، خاصیت ضد آب به آن اضافه کنید.'
            ],
            [
                'question' => 'آیا این پارچه برای دوخت پرده مناسب است؟',
                'answer' => 'بله، این پارچه به دلیل وزن مناسب و افتادگی خوب، گزینه مناسبی برای دوخت پرده است. همچنین تنوع رنگی بالایی دارد که برای دکوراسیون داخلی مناسب است.'
            ]
        ];

        // Create questions with answers
        foreach ($questionsAndAnswers as $index => $qa) {
            // Randomly select users for question and answer (if available)
            $questionUserId = $users->isNotEmpty() ? $users->random()->id : null;
            $answerUserId = $users->isNotEmpty() ? $users->random()->id : null;

            // Create the question
            $question = $product->questions()->create([
                'body' => $qa['question'],
                'user_id' => $questionUserId,
            ]);

            // Create an answer for the question
            $question->answers()->create([
                'body' => Str::limit($qa['answer'], 255),
                'user_id' => $answerUserId,
            ]);

            // For some questions, add a second answer
            if ($index < 2) {
                $secondAnswerUserId = $users->isNotEmpty() ? $users->random()->id : null;
                $replyBody = substr(Faker::sentence(), 0, 250);
                $question->answers()->create([
                    'body' => 'من هم با نظر دوستمون موافقم. ' . $replyBody,
                    'user_id' => $secondAnswerUserId,
                ]);
            }
        }

        $this->command->info("Deleted {$questionCount} questions and their answers.");
        $this->command->info("Five questions with answers created for the product");
    }

    /**
     * Create articles for the product
     */
    private function createArticles(Product $product): void
    {
        // Count existing articles before deletion
        $articleCount = $product->articles()->count();

        // Delete existing articles for this product
        $product->articles()->delete();

        // Create 2 articles with HTML content
        for ($i = 0; $i < 2; $i++) {
            // Generate a Persian title
            $title = Faker::sentence();

            // Generate HTML content
            $content = $this->generateHtmlContent($i);

            // Create article related to the product
            $product->articles()->create([
                'title' => $title,
                'content' => $content,
            ]);

            $this->command->info("Created article '{$title}' for product '{$product->title}'");
        }

        $this->command->info("Deleted {$articleCount} articles.");
        $this->command->info("Two articles created for the product");
    }

    /**
     * Generate HTML content similar to what would be produced by CKEditor.
     *
     * @param int $templateIndex
     * @return string
     */
    private function generateHtmlContent(int $templateIndex): string
    {
        // Use different templates for each article
        if ($templateIndex === 0) {
            return $this->generateArticleTemplate1();
        } else {
            return $this->generateArticleTemplate2();
        }
    }

    /**
     * Generate article template 1 - Basic article with heading, paragraphs and image
     */
    private function generateArticleTemplate1(): string
    {
        $heading = Faker::sentence();
        $paragraph1 = Faker::sentence();
        $paragraph2 = Faker::sentence();
        $paragraph3 = Faker::sentence();

        return <<<HTML
<h2>{$heading}</h2>
<p>{$paragraph1}</p>
<div class="image-container">
    <img src="https://picsum.photos/800/400" alt="تصویر مقاله" />
    <small>توضیحات تصویر پارچه</small>
</div>
<p>{$paragraph2}</p>
<p>{$paragraph3}</p>
HTML;
    }

    /**
     * Generate article template 2 - Article with heading, paragraphs, and list
     */
    private function generateArticleTemplate2(): string
    {
        $heading = Faker::sentence();
        $paragraph1 = Faker::sentence();
        $paragraph2 = Faker::sentence();

        // Generate list items about fabric/cloth
        $listItems = '';
        $clothFeatures = [
            'قابل شستشو در ماشین لباسشویی',
            'مقاوم در برابر چروک',
            'مناسب برای لباس‌های تابستانی',
            'دارای تنوع رنگی بالا'
        ];

        foreach ($clothFeatures as $feature) {
            $listItems .= "<li>{$feature}</li>";
        }

        return <<<HTML
<h2>{$heading}</h2>
<p>{$paragraph1}</p>
<h3>ویژگی‌های پارچه</h3>
<ul>
    {$listItems}
</ul>
<p>{$paragraph2}</p>
HTML;
    }

    /**
     * Create guides for the product
     */
    private function createGuides(Product $product): void
    {
        // Count existing guides before deletion
        $guideCount = $product->guides()->count();

        // Delete existing guides for this product
        $product->guides()->delete();

        // Create 3 guides with specific content for cloth product
        $guides = [
            [
                'title' => 'راهنمای استفاده از پارچه',
                'content' => $this->generateClothUsageGuide(),
                'order' => 1
            ],
            [
                'title' => 'راهنمای نگهداری پارچه',
                'content' => $this->generateClothMaintenanceGuide(),
                'order' => 2
            ],
            [
                'title' => 'نکات مهم درباره پارچه',
                'content' => $this->generateClothTipsGuide(),
                'order' => 3
            ]
        ];

        foreach ($guides as $guide) {
            $product->guides()->create($guide);
            $this->command->info("راهنمای '{$guide['title']}' برای محصول '{$product->title}' ایجاد شد");
        }

        $this->command->info("Deleted {$guideCount} guides.");
        $this->command->info("Three guides created for the product");
    }

    /**
     * Generate cloth usage guide content
     *
     * @return string HTML content
     */
    private function generateClothUsageGuide(): string
    {
        return <<<HTML
<h2>نحوه استفاده از پارچه</h2>
<p>پارچه‌های با کیفیت نیازمند استفاده صحیح هستند تا عمر و زیبایی خود را حفظ کنند. این راهنما به شما کمک می‌کند تا بهترین استفاده را از پارچه خریداری شده داشته باشید.</p>
<div class="steps">
    <h3>مراحل استفاده</h3>
    <ol>
        <li>قبل از برش پارچه، آن را بشویید تا از آب رفتگی احتمالی جلوگیری شود.</li>
        <li>هنگام برش، از قیچی مخصوص پارچه استفاده کنید تا لبه‌ها صاف و تمیز باشند.</li>
        <li>برای دوخت، از سوزن و نخ مناسب با جنس پارچه استفاده کنید.</li>
        <li>پس از دوخت، اتو کردن با درجه حرارت مناسب باعث زیبایی بیشتر می‌شود.</li>
    </ol>
</div>
<div class="image-container">
    <img src="https://picsum.photos/800/400" alt="تصویر راهنمای استفاده از پارچه" />
    <small>نمونه استفاده صحیح از پارچه</small>
</div>
<p>با رعایت این نکات، می‌توانید از پارچه خود به بهترین شکل استفاده کنید و محصولی با کیفیت و ماندگار تولید نمایید.</p>
HTML;
    }

    /**
     * Generate cloth maintenance guide content
     *
     * @return string HTML content
     */
    private function generateClothMaintenanceGuide(): string
    {
        return <<<HTML
<h2>نحوه نگهداری از پارچه</h2>
<p>نگهداری صحیح از پارچه باعث افزایش طول عمر و حفظ کیفیت آن می‌شود. با رعایت نکات زیر، پارچه‌های شما برای مدت طولانی‌تری زیبا و با کیفیت باقی می‌مانند.</p>
<div class="maintenance-tips">
    <h3>نکات نگهداری</h3>
    <ul>
        <li><strong>شستشو:</strong> پارچه را در آب ولرم و با شوینده ملایم بشویید. از مواد سفیدکننده قوی خودداری کنید.</li>
        <li><strong>خشک کردن:</strong> پارچه را در سایه و به صورت افقی خشک کنید تا از تغییر شکل آن جلوگیری شود.</li>
        <li><strong>اتو کردن:</strong> از درجه حرارت مناسب با جنس پارچه استفاده کنید. برای پارچه‌های ظریف، از پارچه نخی روی آن استفاده کنید.</li>
        <li><strong>نگهداری:</strong> پارچه را در محیط خشک و دور از نور مستقیم خورشید نگهداری کنید.</li>
    </ul>
</div>
<div class="warning">
    <h4>هشدار</h4>
    <p>از قرار دادن پارچه در معرض نور مستقیم خورشید یا رطوبت زیاد خودداری کنید. این عوامل می‌توانند باعث رنگ پریدگی و آسیب به الیاف پارچه شوند.</p>
</div>
HTML;
    }

    /**
     * Generate cloth tips guide content
     *
     * @return string HTML content
     */
    private function generateClothTipsGuide(): string
    {
        return <<<HTML
<h2>نکات مهم درباره پارچه</h2>
<p>برای استفاده بهینه از پارچه و تولید محصولات با کیفیت، توجه به نکات زیر ضروری است. این راهنما شامل توصیه‌های کاربردی برای خرید، استفاده و نگهداری پارچه می‌باشد.</p>
<div class="tips">
    <div class="tip">
        <h4>نکته ۱: انتخاب پارچه مناسب</h4>
        <p>برای هر پروژه، پارچه مناسب با آن را انتخاب کنید. برای لباس‌های تابستانی از پارچه‌های نخی و برای لباس‌های زمستانی از پارچه‌های ضخیم‌تر استفاده کنید.</p>
    </div>
    <div class="tip">
        <h4>نکته ۲: آب رفتگی پارچه</h4>
        <p>قبل از دوخت، پارچه را بشویید تا در صورت آب رفتگی، این اتفاق قبل از دوخت رخ دهد. این کار از تغییر سایز لباس پس از شستشو جلوگیری می‌کند.</p>
    </div>
    <div class="tip">
        <h4>نکته ۳: جهت پارچه</h4>
        <p>به جهت بافت پارچه توجه کنید. برش در جهت صحیح باعث می‌شود لباس شکل بهتری داشته باشد و کمتر کش بیاید.</p>
    </div>
</div>
<div class="pro-tip">
    <h4>توصیه ویژه</h4>
    <p>برای پارچه‌های گران‌قیمت، ابتدا روی یک تکه کوچک آزمایش کنید تا از نتیجه نهایی مطمئن شوید. این کار از اتلاف پارچه و هزینه جلوگیری می‌کند.</p>
</div>
HTML;
    }

    /**
     * Ensure all users in the collection have full names
     *
     * @param \Illuminate\Database\Eloquent\Collection $users
     * @return \Illuminate\Database\Eloquent\Collection
     */
    private function ensureUsersHaveFullNames($users)
    {
        // If no users, return empty collection
        if ($users->isEmpty()) {
            return $users;
        }

        // Check each user and update if full_name is null
        foreach ($users as $user) {
            if (empty($user->full_name)) {
                // Generate a Persian name
                $firstName = Faker::firstName();
                $lastName = Faker::lastName();
                $fullName = $firstName . ' ' . $lastName;

                // Update the user
                $user->full_name = $fullName;
                $user->save();

                $this->command->info("Updated user {$user->id} with full name: {$fullName}");
            }
        }

        return $users;
    }
}
