<?php

namespace App\Traits\InvoiceProduct;

use App\Models\Shopping\Invoice;
use App\Models\Shopping\InvoiceProductDetail;
use App\Models\Product\ProductVariant;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * InvoiceProduct Relations Trait
 *
 * This trait contains all relationship methods for the InvoiceProduct model.
 * It helps to separate relationship logic from the core model functionality.
 *
 * @package App\Traits\InvoiceProduct
 */
trait InvoiceProductRelationsTrait
{
    /**
     * Get the invoice that this product belongs to.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function invoice()
    {
        return $this->belongsTo(Invoice::class);
    }

    /**
     * Get the details associated with this invoice product.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function details()
    {
        return $this->hasMany(InvoiceProductDetail::class);
    }

    /**
     * Get the product variation that this invoice product belongs to.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function productVariant()
    {
        return $this->belongsTo(ProductVariant::class, 'variant_id', 'id');
    }
    public function variant()
    {
        return $this->belongsTo(ProductVariant::class, 'variant_id', 'id');
    }

}
