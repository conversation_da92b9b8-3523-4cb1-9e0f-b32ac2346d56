<?php

namespace App\Services\Actions\SellerAccounts;

use App\Exceptions\BusinessException;

/**
 * Delete Seller Account Action
 *
 * Handles the deletion of a seller account.
 */
class DeleteSellerAccount
{
    /**
     * Handle deleting a seller account
     *
     * @param array $data [
     *   'account' => SellerAccount - The account model instance from route binding
     * ]
     * @return bool
     */
    public function handle(array $data): bool
    {
        $user = auth()->user();
        $account = $data['account'];

        // Verify user is a seller
        if (!$user->isSeller()) {
            throw new BusinessException(__('messages.user.not_seller'));
        }

        // Verify ownership (double-check even though Form Request validates this)
        if ($account->user_id !== $user->id) {
            throw new BusinessException(__('messages.seller_accounts.unauthorized'));
        }

        // Prevent deletion of verified accounts (double-check even though Form Request validates this)
        if ($account->verified) {
            throw new BusinessException(__('messages.seller_accounts.cannot_delete_verified'));
        }

        // Delete the account
        return $account->delete();
    }
}
