<?php

namespace App\Services\Actions\SellerAccounts;

use App\Exceptions\BusinessException;
use App\Models\SellerAccounting\SellerAccount;

/**
 * Delete Seller Account Action
 *
 * Handles the deletion of a seller account.
 */
class DeleteSellerAccount
{
    /**
     * Handle deleting a seller account
     *
     * @param array $data [
     *   'account_id' => string - The account ID to delete
     * ]
     * @return bool
     */
    public function handle(array $data): bool
    {
        $user = auth()->user();

        // Verify user is a seller
        if (!$user->isSeller()) {
            throw new BusinessException(__('messages.user.not_seller'));
        }

        // Find the account
        $account = SellerAccount::find($data['account_id']);

        if (!$account) {
            throw new BusinessException(__('messages.seller_accounts.not_found'));
        }

        // Verify ownership
        if ($account->user_id !== $user->id) {
            throw new BusinessException(__('messages.seller_accounts.unauthorized'));
        }

        // Prevent deletion of verified accounts
        if ($account->verified) {
            throw new BusinessException(__('messages.seller_accounts.cannot_delete_verified'));
        }

        // Delete the account
        return $account->delete();
    }
}
