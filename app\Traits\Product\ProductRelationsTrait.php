<?php

namespace App\Traits\Product;

use App\Models\Content\Article;
use App\Models\Product\Category;
use App\Models\Shopping\InvoiceProduct;
use App\Models\UserInteraction\Comment;
use App\Models\Shopping\DeliveryMethod;
use App\Models\Content\Gallery;
use App\Models\Content\Guide;
use App\Models\Product\Guarantee;
use App\Models\Content\Keyword;
use App\Models\Product\ProductDetail;
use App\Models\Product\ProductVariant;
use App\Models\UserInteraction\Question;
use App\Models\User\Shop;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphMany;


/**
 * Product Relations Trait
 *
 * This trait contains all relationship methods for the Product model.
 * It helps to separate relationship logic from the core model functionality.
 *
 * @package App\Traits\Product
 */
trait ProductRelationsTrait
{
    /**
     * Get the gallery images associated with this product.
     *
     * @return \Illuminate\Database\Eloquent\Relations\MorphMany
     */
    public function gallery()
    {
        return $this->morphMany(Gallery::class, 'imageable');
    }

    /**
     * Get the product details associated with this product.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function details()
    {
        return $this->hasMany(ProductDetail::class);
    }

    /**
     * Get the keywords associated with this product.
     *
     * @return \Illuminate\Database\Eloquent\Relations\MorphMany
     */
    public function keywords()
    {
        return $this->morphMany(Keyword::class, 'keywordable');
    }

    /**
     * Get the variants associated with this product.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function variants()
    {
        return $this->hasMany(ProductVariant::class);
    }

    /**
     * Get the category this product belongs to.
     *
     * @return \MongoDB\Laravel\Relations\BelongsTo
     */
    public function category()
    {
        return $this->belongsTo(Category::class, );
    }

    /**
     * Get the comments associated with this product.
     *
     * @return \Illuminate\Database\Eloquent\Relations\MorphMany
     */
    public function comments()
    {
        return $this->morphMany(Comment::class, 'commentable');
    }

    /**
     * Get the questions associated with this product.
     *
     * @return \Illuminate\Database\Eloquent\Relations\MorphMany
     */
    public function questions()
    {
        return $this->morphMany(Question::class, 'questionable');
    }

    /**
     * Get the delivery methods available for this product.
     *
     * Note: This should be a belongsToMany relationship, which stores delivery_method_ids
     * directly in the products collection.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsToMany
     */
    public function deliveryMethods()
    {
        return $this->belongsToMany(DeliveryMethod::class);
    }

    /**
     * Get the guarantees associated with this product.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsToMany
     */
    public function guarantees()
    {
        return $this->belongsToMany(Guarantee::class);
    }

    /**
     * Get the articles associated with this product.
     *
     * @return \Illuminate\Database\Eloquent\Relations\MorphMany
     */
    public function articles()
    {
        return $this->morphMany(Article::class, 'articleable');
    }

    /**
     * Get the shop that this product belongs to.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function shop()
    {
        return $this->belongsTo(Shop::class);
    }

    /**
     * Get the guides associated with this product.
     *
     * @return \Illuminate\Database\Eloquent\Relations\MorphMany
     */
    public function guides()
    {
        return $this->morphMany(Guide::class, 'guideable');
    }

    public function invoiceProducts()
    {
        return $this->hasMany(InvoiceProduct::class);
    }
}
