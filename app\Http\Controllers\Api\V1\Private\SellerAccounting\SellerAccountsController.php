<?php

namespace App\Http\Controllers\Api\V1\Private\SellerAccounting;

use App\Http\Controllers\Api\BaseController;
use App\Http\Requests\SellerAccount\CreateSellerAccountRequest;
use App\Http\Resources\SellerAccount\SellerAccountResource;
use App\Services\Actions\SellerAccounts\CreateSellerAccount;
use App\Services\Actions\SellerAccounts\GetSellerAccounts;

class SellerAccountsController extends BaseController
{
    /**
     * Gets All Seller accounts
     * @param \App\Services\Actions\SellerAccounts\GetSellerAccounts $action
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(GetSellerAccounts $action)
    {
        $accounts = $action->handle();
        return $this->sendResponse(
            SellerAccountResource::collection($accounts),
            __('messages.seller_accounts.fetch_done')
        );
    }
    /**
     * creates new Seller accounts
     * @param CreateSellerAccount $action
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(CreateSellerAccountRequest $request, CreateSellerAccount $action)
    {
        $accounts = $action->handle($request->validated());
        return $this->sendResponse(
            new SellerAccountResource($accounts),
            __('messages.seller_accounts.create')
        );
    }
}
