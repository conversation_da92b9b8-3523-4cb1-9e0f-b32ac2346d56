<?php

namespace App\Services\Actions\Cart;

use App\Models\Shopping\ShoppingCart;
use App\Traits\Disconunt\AppliesDiscountCode;

/**
 * GetCart Action
 *
 * Retrieves the current user's shopping cart with all items.
 * If the cart doesn't exist, it creates an empty one.
 */
class GetCart
{
    use AppliesDiscountCode;
    /**
     * Handle retrieving the user's cart.
     *
     * @param array $data Empty array as we don't need any data from the request
     * @return array
     */
    public function handle(array $data): array
    {
        $userId = auth()->id();

        $cart = ShoppingCart::where('user_id', $userId)
            ->first();

        if (!$cart) {
            $cart = ShoppingCart::create(['user_id' => $userId]);
        }

        $cart->load(
            'items.productVariant.product',
            'items.productVariant.attributes'
        );
        $items = $cart->items;
        $subtotal = $items->sum(fn($item) => $item->price * $item->quantity);
        $totalDiscount = $items->sum(fn($item) => $item->discount);
        $total = $items->sum(fn($item) => $item->total);

        $discountAmount = 0;
        $discountCode = null;
        $finalTotal = $total;
        if (!empty($data['discount_code'])) {
            [$discountAmount, $finalTotal, $discountCode] = $this->applyDiscountToCart($data['discount_code'], $total);
        }

        return [
            'model' => $cart,
            'subtotal' => $subtotal,
            'total_discount' => $totalDiscount + $discountAmount,
            'total' => $finalTotal

        ];
    }
}
